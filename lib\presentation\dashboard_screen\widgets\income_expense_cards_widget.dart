import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../core/app_export.dart';

class IncomeExpenseCardsWidget extends StatelessWidget {
  final double totalIncome;
  final double totalExpense;
  final double incomePercentageChange;
  final double expensePercentageChange;

  const IncomeExpenseCardsWidget({
    Key? key,
    required this.totalIncome,
    required this.totalExpense,
    required this.incomePercentageChange,
    required this.expensePercentageChange,
  }) : super(key: key);

  String _formatCurrency(double amount) {
    return FormattingService.instance.formatCurrency(amount);
  }

  Widget _buildCard({
    required String title,
    required double amount,
    required double percentageChange,
    required Color color,
    required String iconName,
    required bool isDark,
  }) {
    final bool isPositive = percentageChange >= 0;

    return Expanded(
      child: Container(
        padding: EdgeInsets.all(4.w),
        decoration: BoxDecoration(
          color: isDark ? AppTheme.cardSurfaceDark : AppTheme.cardSurface,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: color.withValues(alpha: 0.2),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: isDark ? AppTheme.shadowDark : AppTheme.shadowLight,
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(2.w),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: CustomIconWidget(
                    iconName: iconName,
                    color: color,
                    size: 20,
                  ),
                ),
                SizedBox(width: 2.w),
                Expanded(
                  child: Text(
                    title,
                    style: GoogleFonts.inter(
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w500,
                      color: isDark
                          ? AppTheme.textMediumEmphasisDark
                          : AppTheme.textMediumEmphasisLight,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 2.h),
            Text(
              _formatCurrency(amount),
              style: AppTheme.financialAmountStyle(
                isLight: !isDark,
                fontSize: 18.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 1.h),
            Row(
              children: [
                CustomIconWidget(
                  iconName: isPositive ? 'trending_up' : 'trending_down',
                  color: isPositive ? AppTheme.successGreen : AppTheme.alertRed,
                  size: 16,
                ),
                SizedBox(width: 1.w),
                Text(
                  '${isPositive ? '+' : ''}${percentageChange.toStringAsFixed(1)}%',
                  style: GoogleFonts.inter(
                    fontSize: 11.sp,
                    fontWeight: FontWeight.w500,
                    color:
                        isPositive ? AppTheme.successGreen : AppTheme.alertRed,
                  ),
                ),
                SizedBox(width: 1.w),
                Expanded(
                  child: Text(
                    'vs last month',
                    style: GoogleFonts.inter(
                      fontSize: 10.sp,
                      fontWeight: FontWeight.w400,
                      color: isDark
                          ? AppTheme.textDisabledDark
                          : AppTheme.textDisabledLight,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return ListenableBuilder(
      listenable: FormattingService.instance,
      builder: (context, child) {
        return Padding(
          padding: EdgeInsets.symmetric(horizontal: 4.w),
          child: Row(
            children: [
              _buildCard(
                title: 'Income',
                amount: totalIncome,
                percentageChange: incomePercentageChange,
                color: AppTheme.successGreen,
                iconName: 'arrow_downward',
                isDark: isDark,
              ),
              SizedBox(width: 3.w),
              _buildCard(
                title: 'Expense',
                amount: totalExpense,
                percentageChange: expensePercentageChange,
                color: AppTheme.alertRed,
                iconName: 'arrow_upward',
                isDark: isDark,
              ),
            ],
          ),
        );
      },
    );
  }
}
