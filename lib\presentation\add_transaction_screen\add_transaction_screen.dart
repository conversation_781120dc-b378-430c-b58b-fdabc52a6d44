import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import '../../core/services/ad_service.dart';
import './widgets/account_dropdown.dart';
import './widgets/amount_input_field.dart';
import './widgets/category_selector.dart';
import './widgets/date_time_picker.dart';
import './widgets/notes_input_field.dart';
import './widgets/recurring_toggle.dart';
import './widgets/transaction_type_toggle.dart';

class AddTransactionScreen extends StatefulWidget {
  const AddTransactionScreen({Key? key}) : super(key: key);

  @override
  State<AddTransactionScreen> createState() => _AddTransactionScreenState();
}

class _AddTransactionScreenState extends State<AddTransactionScreen> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _notesController = TextEditingController();

  bool _isIncome = false;
  String? _selectedCategory;
  Account? _selectedAccount;
  DateTime _selectedDate = DateTime.now();
  TimeOfDay _selectedTime = TimeOfDay.now();
  bool _isRecurring = false;
  String? _selectedFrequency;
  bool _isLoading = false;
  String? _amountError;

  late final FinancialDataManager _financialDataManager;

  @override
  void initState() {
    super.initState();
    _financialDataManager = FinancialDataManager.instance;
  }

  @override
  void dispose() {
    _amountController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: FormattingService.instance,
      builder: (context, child) {
        return Scaffold(
      backgroundColor: AppTheme.backgroundLight,
      appBar: _buildAppBar(),
      body: _buildBody(),
        );
      },
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: AppTheme.backgroundLight,
      elevation: 0,
      leading: IconButton(
        onPressed: () => Navigator.pop(context),
        icon: CustomIconWidget(
          iconName: 'close',
          color: AppTheme.textHighEmphasisLight,
          size: 24,
        ),
      ),
      title: Text(
        'Add Transaction',
        style: AppTheme.lightTheme.textTheme.headlineSmall?.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ),
      actions: [
        Container(
          margin: EdgeInsets.only(right: 4.w),
          child: ElevatedButton(
            onPressed: _canSave() ? _saveTransaction : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: _canSave()
                  ? AppTheme.primaryTeal
                  : AppTheme.neutralGray.withValues(alpha: 0.3),
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 1.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: _isLoading
                ? SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : Text(
                    'Save',
                    style: AppTheme.lightTheme.textTheme.titleSmall?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
          ),
        ),
      ],
    );
  }

  Widget _buildBody() {
    return Form(
      key: _formKey,
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 2.h),

            // Transaction Type Toggle
            TransactionTypeToggle(
              isIncome: _isIncome,
              onToggle: (isIncome) {
                setState(() {
                  _isIncome = isIncome;
                  _selectedCategory = null; // Reset category when type changes
                });
              },
            ),

            SizedBox(height: 2.h),

            // Amount Input
            AmountInputField(
              controller: _amountController,
              isIncome: _isIncome,
              errorText: _amountError,
            ),

            SizedBox(height: 2.h),

            // Category Selector
            CategorySelector(
              selectedCategory: _selectedCategory,
              isIncome: _isIncome,
              onCategorySelected: (category) {
                setState(() {
                  _selectedCategory = category;
                });
              },
            ),

            SizedBox(height: 2.h),

            // Account Dropdown
            AccountDropdown(
              selectedAccount: _selectedAccount,
              onAccountSelected: (account) {
                setState(() {
                  _selectedAccount = account;
                });
              },
            ),

            SizedBox(height: 2.h),

            // Date and Time Picker
            DateTimePicker(
              selectedDate: _selectedDate,
              selectedTime: _selectedTime,
              onDateChanged: (date) {
                setState(() {
                  _selectedDate = date;
                });
              },
              onTimeChanged: (time) {
                setState(() {
                  _selectedTime = time;
                });
              },
            ),

            SizedBox(height: 2.h),

            // Notes Input
            NotesInputField(
              controller: _notesController,
              maxLength: 200,
            ),

            SizedBox(height: 2.h),

            // Recurring Toggle
            RecurringToggle(
              isRecurring: _isRecurring,
              selectedFrequency: _selectedFrequency,
              onToggle: (isRecurring) {
                setState(() {
                  _isRecurring = isRecurring;
                  if (!isRecurring) {
                    _selectedFrequency = null;
                  }
                });
              },
              onFrequencyChanged: (frequency) {
                setState(() {
                  _selectedFrequency = frequency;
                });
              },
            ),

            SizedBox(height: 4.h),
          ],
        ),
      ),
    );
  }

  bool _canSave() {
    return _amountController.text.isNotEmpty &&
        double.tryParse(_amountController.text) != null &&
        double.parse(_amountController.text) > 0 &&
        _selectedCategory != null &&
        // Account selection is now optional
        (!_isRecurring || _selectedFrequency != null) &&
        !_isLoading;
  }

  Future<void> _saveTransaction() async {
    if (!_canSave()) return;

    setState(() {
      _isLoading = true;
      _amountError = null;
    });

    try {
      // Validate amount
      final amount = double.tryParse(_amountController.text);
      if (amount == null || amount <= 0) {
        setState(() {
          _amountError = 'Please enter a valid amount';
          _isLoading = false;
        });
        return;
      }

      // Combine date and time
      final transactionDateTime = DateTime(
        _selectedDate.year,
        _selectedDate.month,
        _selectedDate.day,
        _selectedTime.hour,
        _selectedTime.minute,
      );

      // Handle account selection - create default if none selected
      Account? accountToUse = _selectedAccount;
      if (accountToUse == null) {
        // Create or get default "General" account
        accountToUse = await _getOrCreateDefaultAccount();
        if (accountToUse == null) {
          setState(() {
            _isLoading = false;
          });
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Failed to create default account'),
                backgroundColor: AppTheme.errorRed,
              ),
            );
          }
          return;
        }
      }

      // Save transaction using financial data manager
      final success = await _financialDataManager.addTransaction(
        type: _isIncome ? 'income' : 'expense',
        amount: amount,
        category: _selectedCategory!,
        account: accountToUse.name,
        description: _notesController.text.trim().isNotEmpty
            ? _notesController.text.trim()
            : null,
        notes: _notesController.text.trim().isNotEmpty
            ? _notesController.text.trim()
            : null,
        date: transactionDateTime,
        isRecurring: _isRecurring,
        recurringFrequency: _selectedFrequency,
      );

      if (!success) {
        setState(() {
          _isLoading = false;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  CustomIconWidget(
                    iconName: 'error',
                    color: Colors.white,
                    size: 20,
                  ),
                  SizedBox(width: 2.w),
                  Text(
                    _financialDataManager.error ?? 'Failed to save transaction',
                    style: AppTheme.lightTheme.textTheme.titleSmall?.copyWith(
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
              backgroundColor: AppTheme.alertRed,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          );
        }
        return;
      }

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                CustomIconWidget(
                  iconName: 'check_circle',
                  color: Colors.white,
                  size: 20,
                ),
                SizedBox(width: 2.w),
                Text(
                  'Transaction saved successfully!',
                  style: AppTheme.lightTheme.textTheme.titleSmall?.copyWith(
                    color: Colors.white,
                  ),
                ),
              ],
            ),
            backgroundColor: AppTheme.successGreen,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            duration: const Duration(seconds: 2),
          ),
        );

        // Show interstitial ad
        AdService().showInterstitialAd();

        // Navigate back to dashboard
        Navigator.pop(context, true);
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                CustomIconWidget(
                  iconName: 'error',
                  color: Colors.white,
                  size: 20,
                ),
                SizedBox(width: 2.w),
                Text(
                  'Failed to save transaction. Please try again.',
                  style: AppTheme.lightTheme.textTheme.titleSmall?.copyWith(
                    color: Colors.white,
                  ),
                ),
              ],
            ),
            backgroundColor: AppTheme.alertRed,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        );
      }
    }
  }

  /// Get or create a default "General" account for transactions without account selection
  Future<Account?> _getOrCreateDefaultAccount() async {
    try {
      // First, check if a "General" account already exists
      final existingAccount = _financialDataManager.getAccountByName('General');
      if (existingAccount != null) {
        return existingAccount;
      }

      // Create a new "General" account
      final success = await _financialDataManager.addAccount(
        name: 'General',
        accountType: 'cash',
        balance: 0.0,
        accountIcon: 'account_balance_wallet',
        accountColor: '#2196F3',
      );

      if (success) {
        // Refresh accounts and get the newly created account
        await _financialDataManager.refreshAccounts();
        return _financialDataManager.getAccountByName('General');
      }

      return null;
    } catch (e) {
      debugPrint('Error creating default account: $e');
      return null;
    }
  }
}
