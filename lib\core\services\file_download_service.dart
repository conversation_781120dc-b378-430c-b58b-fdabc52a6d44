import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:excel/excel.dart';
import 'package:open_file/open_file.dart';
import 'package:intl/intl.dart';

import '../models/transaction_model.dart';

class FileDownloadService {
  static const String _appName = 'FinTrack';

  /// Request storage permissions
  static Future<bool> _requestPermissions() async {
    if (Platform.isAndroid) {
      final status = await Permission.storage.request();
      if (status.isDenied) {
        final manageStatus = await Permission.manageExternalStorage.request();
        return manageStatus.isGranted;
      }
      return status.isGranted;
    }
    return true; // iOS doesn't need explicit storage permissions for app documents
  }

  /// Get the downloads directory
  static Future<Directory> _getDownloadsDirectory() async {
    if (Platform.isAndroid) {
      // Try to get the Downloads directory
      Directory? downloadsDir = Directory('/storage/emulated/0/Download');
      if (await downloadsDir.exists()) {
        return downloadsDir;
      }
      
      // Fallback to external storage directory
      final externalDir = await getExternalStorageDirectory();
      if (externalDir != null) {
        final downloadsPath = Directory('${externalDir.path}/Download');
        if (!await downloadsPath.exists()) {
          await downloadsPath.create(recursive: true);
        }
        return downloadsPath;
      }
    }
    
    // Fallback to app documents directory
    return await getApplicationDocumentsDirectory();
  }

  /// Generate PDF statement
  static Future<Uint8List> _generatePDF({
    required List<Transaction> transactions,
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    final pdf = pw.Document();
    
    // Calculate totals
    double totalIncome = 0;
    double totalExpense = 0;
    
    for (final transaction in transactions) {
      if (transaction.type == 'income') {
        totalIncome += transaction.amount;
      } else {
        totalExpense += transaction.amount;
      }
    }
    
    final netBalance = totalIncome - totalExpense;
    final dateFormatter = DateFormat('dd/MM/yyyy');
    
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(32),
        build: (pw.Context context) {
          return [
            // Header
            pw.Header(
              level: 0,
              child: pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  pw.Text(
                    'FinTrack Statement',
                    style: pw.TextStyle(
                      fontSize: 24,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                  pw.Text(
                    'Generated: ${DateFormat('dd/MM/yyyy HH:mm').format(DateTime.now())}',
                    style: const pw.TextStyle(fontSize: 12),
                  ),
                ],
              ),
            ),
            
            pw.SizedBox(height: 20),
            
            // Period
            pw.Container(
              padding: const pw.EdgeInsets.all(16),
              decoration: pw.BoxDecoration(
                border: pw.Border.all(color: PdfColors.grey300),
                borderRadius: pw.BorderRadius.circular(8),
              ),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text(
                    'Statement Period',
                    style: pw.TextStyle(
                      fontSize: 16,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                  pw.SizedBox(height: 8),
                  pw.Text('From: ${dateFormatter.format(startDate)}'),
                  pw.Text('To: ${dateFormatter.format(endDate)}'),
                ],
              ),
            ),
            
            pw.SizedBox(height: 20),
            
            // Summary
            pw.Container(
              padding: const pw.EdgeInsets.all(16),
              decoration: pw.BoxDecoration(
                border: pw.Border.all(color: PdfColors.grey300),
                borderRadius: pw.BorderRadius.circular(8),
              ),
              child: pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceAround,
                children: [
                  pw.Column(
                    children: [
                      pw.Text(
                        'Total Income',
                        style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                      ),
                      pw.Text(
                        '₹${totalIncome.toStringAsFixed(2)}',
                        style: const pw.TextStyle(color: PdfColors.green),
                      ),
                    ],
                  ),
                  pw.Column(
                    children: [
                      pw.Text(
                        'Total Expense',
                        style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                      ),
                      pw.Text(
                        '₹${totalExpense.toStringAsFixed(2)}',
                        style: const pw.TextStyle(color: PdfColors.red),
                      ),
                    ],
                  ),
                  pw.Column(
                    children: [
                      pw.Text(
                        'Net Balance',
                        style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                      ),
                      pw.Text(
                        '₹${netBalance.toStringAsFixed(2)}',
                        style: pw.TextStyle(
                          color: netBalance >= 0 ? PdfColors.green : PdfColors.red,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            pw.SizedBox(height: 30),
            
            // Transactions Table
            pw.Text(
              'Transaction Details',
              style: pw.TextStyle(
                fontSize: 18,
                fontWeight: pw.FontWeight.bold,
              ),
            ),
            
            pw.SizedBox(height: 10),
            
            pw.Table(
              border: pw.TableBorder.all(color: PdfColors.grey300),
              columnWidths: {
                0: const pw.FlexColumnWidth(2),
                1: const pw.FlexColumnWidth(2),
                2: const pw.FlexColumnWidth(2),
                3: const pw.FlexColumnWidth(1.5),
                4: const pw.FlexColumnWidth(1.5),
              },
              children: [
                // Header
                pw.TableRow(
                  decoration: const pw.BoxDecoration(color: PdfColors.grey100),
                  children: [
                    pw.Padding(
                      padding: const pw.EdgeInsets.all(8),
                      child: pw.Text(
                        'Date',
                        style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                      ),
                    ),
                    pw.Padding(
                      padding: const pw.EdgeInsets.all(8),
                      child: pw.Text(
                        'Category',
                        style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                      ),
                    ),
                    pw.Padding(
                      padding: const pw.EdgeInsets.all(8),
                      child: pw.Text(
                        'Account',
                        style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                      ),
                    ),
                    pw.Padding(
                      padding: const pw.EdgeInsets.all(8),
                      child: pw.Text(
                        'Type',
                        style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                      ),
                    ),
                    pw.Padding(
                      padding: const pw.EdgeInsets.all(8),
                      child: pw.Text(
                        'Amount',
                        style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                      ),
                    ),
                  ],
                ),
                
                // Data rows
                ...transactions.map((transaction) {
                  return pw.TableRow(
                    children: [
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text(dateFormatter.format(transaction.date)),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text(transaction.category),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text(transaction.account),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text(
                          transaction.type.toUpperCase(),
                          style: pw.TextStyle(
                            color: transaction.type == 'income' 
                                ? PdfColors.green 
                                : PdfColors.red,
                          ),
                        ),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text(
                          '₹${transaction.amount.toStringAsFixed(2)}',
                          style: pw.TextStyle(
                            color: transaction.type == 'income' 
                                ? PdfColors.green 
                                : PdfColors.red,
                          ),
                        ),
                      ),
                    ],
                  );
                }).toList(),
              ],
            ),
          ];
        },
      ),
    );
    
    return await pdf.save();
  }

  /// Generate Excel statement
  static Future<Uint8List> _generateExcel({
    required List<Transaction> transactions,
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    final excel = Excel.createExcel();
    final sheet = excel['Statement'];
    
    // Remove default sheet
    excel.delete('Sheet1');
    
    final dateFormatter = DateFormat('dd/MM/yyyy');
    
    // Header
    sheet.cell(CellIndex.indexByString('A1')).value = TextCellValue('FinTrack Statement');
    sheet.cell(CellIndex.indexByString('A2')).value = TextCellValue('Period: ${dateFormatter.format(startDate)} - ${dateFormatter.format(endDate)}');
    sheet.cell(CellIndex.indexByString('A3')).value = TextCellValue('Generated: ${DateFormat('dd/MM/yyyy HH:mm').format(DateTime.now())}');
    
    // Calculate totals
    double totalIncome = 0;
    double totalExpense = 0;
    
    for (final transaction in transactions) {
      if (transaction.type == 'income') {
        totalIncome += transaction.amount;
      } else {
        totalExpense += transaction.amount;
      }
    }
    
    // Summary
    sheet.cell(CellIndex.indexByString('A5')).value = TextCellValue('Summary');
    sheet.cell(CellIndex.indexByString('A6')).value = TextCellValue('Total Income:');
    sheet.cell(CellIndex.indexByString('B6')).value = DoubleCellValue(totalIncome);
    sheet.cell(CellIndex.indexByString('A7')).value = TextCellValue('Total Expense:');
    sheet.cell(CellIndex.indexByString('B7')).value = DoubleCellValue(totalExpense);
    sheet.cell(CellIndex.indexByString('A8')).value = TextCellValue('Net Balance:');
    sheet.cell(CellIndex.indexByString('B8')).value = DoubleCellValue(totalIncome - totalExpense);
    
    // Transaction headers
    int startRow = 10;
    sheet.cell(CellIndex.indexByString('A$startRow')).value = TextCellValue('Date');
    sheet.cell(CellIndex.indexByString('B$startRow')).value = TextCellValue('Category');
    sheet.cell(CellIndex.indexByString('C$startRow')).value = TextCellValue('Account');
    sheet.cell(CellIndex.indexByString('D$startRow')).value = TextCellValue('Type');
    sheet.cell(CellIndex.indexByString('E$startRow')).value = TextCellValue('Amount');
    sheet.cell(CellIndex.indexByString('F$startRow')).value = TextCellValue('Description');
    
    // Transaction data
    for (int i = 0; i < transactions.length; i++) {
      final transaction = transactions[i];
      final row = startRow + 1 + i;
      
      sheet.cell(CellIndex.indexByString('A$row')).value = TextCellValue(dateFormatter.format(transaction.date));
      sheet.cell(CellIndex.indexByString('B$row')).value = TextCellValue(transaction.category);
      sheet.cell(CellIndex.indexByString('C$row')).value = TextCellValue(transaction.account);
      sheet.cell(CellIndex.indexByString('D$row')).value = TextCellValue(transaction.type.toUpperCase());
      sheet.cell(CellIndex.indexByString('E$row')).value = DoubleCellValue(transaction.amount);
      sheet.cell(CellIndex.indexByString('F$row')).value = TextCellValue(transaction.description ?? '');
    }
    
    return Uint8List.fromList(excel.encode()!);
  }

  /// Download statement file
  static Future<bool> downloadStatement({
    required BuildContext context,
    required List<Transaction> transactions,
    required DateTime startDate,
    required DateTime endDate,
    required String format,
  }) async {
    try {
      // Request permissions
      final hasPermission = await _requestPermissions();
      if (!hasPermission) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Storage permission is required to download files'),
            backgroundColor: Colors.red,
          ),
        );
        return false;
      }

      // Generate filename
      final dateFormatter = DateFormat('yyyy-MM-dd');
      final filename = 'FinTrack_Statement_${dateFormatter.format(startDate)}_to_${dateFormatter.format(endDate)}.$format';

      // Generate file content
      Uint8List fileBytes;
      if (format == 'pdf') {
        fileBytes = await _generatePDF(
          transactions: transactions,
          startDate: startDate,
          endDate: endDate,
        );
      } else {
        fileBytes = await _generateExcel(
          transactions: transactions,
          startDate: startDate,
          endDate: endDate,
        );
      }

      // Get downloads directory
      final downloadsDir = await _getDownloadsDirectory();
      final file = File('${downloadsDir.path}/$filename');

      // Write file
      await file.writeAsBytes(fileBytes);

      // Show success message with option to open
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Statement saved: $filename'),
          backgroundColor: Colors.green,
          action: SnackBarAction(
            label: 'Open',
            textColor: Colors.white,
            onPressed: () async {
              try {
                await OpenFile.open(file.path);
              } catch (e) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('File saved to: ${file.path}'),
                    backgroundColor: Colors.blue,
                  ),
                );
              }
            },
          ),
        ),
      );

      return true;
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error downloading statement: $e'),
          backgroundColor: Colors.red,
        ),
      );
      return false;
    }
  }
}
