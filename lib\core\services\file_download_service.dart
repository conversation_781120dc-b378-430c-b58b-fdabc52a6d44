import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:excel/excel.dart';
import 'package:open_file/open_file.dart';
import 'package:intl/intl.dart';

import '../models/transaction_model.dart';

class FileDownloadService {
  static const String _appName = 'FinTrack';
  static bool _isRequestingPermissions = false;

  /// Request storage permissions with proper handling and conflict prevention
  static Future<bool> _requestPermissions() async {
    if (Platform.isAndroid) {
      // Prevent multiple simultaneous permission requests
      if (_isRequestingPermissions) {
        print('Permission request already in progress, waiting...');
        // Wait for the current request to finish
        int waitCount = 0;
        while (_isRequestingPermissions && waitCount < 20) { // Max 10 seconds wait
          await Future.delayed(const Duration(milliseconds: 500));
          waitCount++;
        }
      }

      _isRequestingPermissions = true;

      try {
        // Check current status first
        final storageStatus = await Permission.storage.status;
        final manageStatus = await Permission.manageExternalStorage.status;

        print('Current permissions - Storage: $storageStatus, Manage: $manageStatus');

        // If already granted, return true
        if (storageStatus.isGranted || manageStatus.isGranted) {
          return true;
        }

        // Request permissions one at a time to avoid conflicts
        if (storageStatus.isDenied) {
          print('Requesting storage permission...');
          final newStorageStatus = await Permission.storage.request();
          print('Storage permission result: $newStorageStatus');
          if (newStorageStatus.isGranted) {
            return true;
          }
        }

        // If storage permission failed, try manage external storage
        if (manageStatus.isDenied) {
          print('Requesting manage external storage permission...');
          final newManageStatus = await Permission.manageExternalStorage.request();
          print('Manage external storage permission result: $newManageStatus');
          return newManageStatus.isGranted;
        }

        return false;
      } catch (e) {
        print('Permission request error: $e');
        return false;
      } finally {
        _isRequestingPermissions = false;
      }
    }
    return true; // iOS doesn't need explicit storage permissions for app documents
  }

  /// Get the downloads directory with better error handling
  static Future<Directory> _getDownloadsDirectory() async {
    if (Platform.isAndroid) {
      try {
        // Try multiple download directory paths
        final possiblePaths = [
          '/storage/emulated/0/Download',
          '/storage/emulated/0/Downloads',
          '/sdcard/Download',
          '/sdcard/Downloads',
        ];

        for (final path in possiblePaths) {
          final dir = Directory(path);
          if (await dir.exists()) {
            // Test if we can write to this directory
            try {
              final testFile = File('${dir.path}/.test_write');
              await testFile.writeAsString('test');
              await testFile.delete();
              return dir;
            } catch (e) {
              // Can't write to this directory, try next
              continue;
            }
          }
        }

        // Fallback to external storage directory
        final externalDir = await getExternalStorageDirectory();
        if (externalDir != null) {
          final downloadsPath = Directory('${externalDir.path}/Downloads');
          if (!await downloadsPath.exists()) {
            await downloadsPath.create(recursive: true);
          }
          return downloadsPath;
        }
      } catch (e) {
        print('Error getting downloads directory: $e');
      }
    }

    // Fallback to app documents directory
    return await getApplicationDocumentsDirectory();
  }

  /// Generate PDF statement
  static Future<Uint8List> _generatePDF({
    required List<Transaction> transactions,
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    final pdf = pw.Document();
    
    // Calculate totals
    double totalIncome = 0;
    double totalExpense = 0;
    
    for (final transaction in transactions) {
      if (transaction.type == 'income') {
        totalIncome += transaction.amount;
      } else {
        totalExpense += transaction.amount;
      }
    }
    
    final netBalance = totalIncome - totalExpense;
    final dateFormatter = DateFormat('dd/MM/yyyy');
    
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(32),
        build: (pw.Context context) {
          return [
            // Header
            pw.Header(
              level: 0,
              child: pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  pw.Text(
                    'FinTrack Statement',
                    style: pw.TextStyle(
                      fontSize: 24,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                  pw.Text(
                    'Generated: ${DateFormat('dd/MM/yyyy HH:mm').format(DateTime.now())}',
                    style: const pw.TextStyle(fontSize: 12),
                  ),
                ],
              ),
            ),
            
            pw.SizedBox(height: 20),
            
            // Period
            pw.Container(
              padding: const pw.EdgeInsets.all(16),
              decoration: pw.BoxDecoration(
                border: pw.Border.all(color: PdfColors.grey300),
                borderRadius: pw.BorderRadius.circular(8),
              ),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text(
                    'Statement Period',
                    style: pw.TextStyle(
                      fontSize: 16,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                  pw.SizedBox(height: 8),
                  pw.Text('From: ${dateFormatter.format(startDate)}'),
                  pw.Text('To: ${dateFormatter.format(endDate)}'),
                ],
              ),
            ),
            
            pw.SizedBox(height: 20),
            
            // Summary
            pw.Container(
              padding: const pw.EdgeInsets.all(16),
              decoration: pw.BoxDecoration(
                border: pw.Border.all(color: PdfColors.grey300),
                borderRadius: pw.BorderRadius.circular(8),
              ),
              child: pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceAround,
                children: [
                  pw.Column(
                    children: [
                      pw.Text(
                        'Total Income',
                        style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                      ),
                      pw.Text(
                        'INR ${totalIncome.toStringAsFixed(2)}',
                        style: const pw.TextStyle(color: PdfColors.green),
                      ),
                    ],
                  ),
                  pw.Column(
                    children: [
                      pw.Text(
                        'Total Expense',
                        style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                      ),
                      pw.Text(
                        'INR ${totalExpense.toStringAsFixed(2)}',
                        style: const pw.TextStyle(color: PdfColors.red),
                      ),
                    ],
                  ),
                  pw.Column(
                    children: [
                      pw.Text(
                        'Net Balance',
                        style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                      ),
                      pw.Text(
                        'INR ${netBalance.toStringAsFixed(2)}',
                        style: pw.TextStyle(
                          color: netBalance >= 0 ? PdfColors.green : PdfColors.red,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            pw.SizedBox(height: 30),
            
            // Transactions Table
            pw.Text(
              'Transaction Details',
              style: pw.TextStyle(
                fontSize: 18,
                fontWeight: pw.FontWeight.bold,
              ),
            ),
            
            pw.SizedBox(height: 10),
            
            pw.Table(
              border: pw.TableBorder.all(color: PdfColors.grey300),
              columnWidths: {
                0: const pw.FlexColumnWidth(2),
                1: const pw.FlexColumnWidth(2),
                2: const pw.FlexColumnWidth(2),
                3: const pw.FlexColumnWidth(1.5),
                4: const pw.FlexColumnWidth(1.5),
              },
              children: [
                // Header
                pw.TableRow(
                  decoration: const pw.BoxDecoration(color: PdfColors.grey100),
                  children: [
                    pw.Padding(
                      padding: const pw.EdgeInsets.all(8),
                      child: pw.Text(
                        'Date',
                        style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                      ),
                    ),
                    pw.Padding(
                      padding: const pw.EdgeInsets.all(8),
                      child: pw.Text(
                        'Category',
                        style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                      ),
                    ),
                    pw.Padding(
                      padding: const pw.EdgeInsets.all(8),
                      child: pw.Text(
                        'Account',
                        style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                      ),
                    ),
                    pw.Padding(
                      padding: const pw.EdgeInsets.all(8),
                      child: pw.Text(
                        'Type',
                        style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                      ),
                    ),
                    pw.Padding(
                      padding: const pw.EdgeInsets.all(8),
                      child: pw.Text(
                        'Amount',
                        style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                      ),
                    ),
                  ],
                ),
                
                // Data rows
                ...transactions.map((transaction) {
                  return pw.TableRow(
                    children: [
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text(dateFormatter.format(transaction.date)),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text(transaction.category),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text(transaction.account),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text(
                          transaction.type.toUpperCase(),
                          style: pw.TextStyle(
                            color: transaction.type == 'income' 
                                ? PdfColors.green 
                                : PdfColors.red,
                          ),
                        ),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text(
                          'INR ${transaction.amount.toStringAsFixed(2)}',
                          style: pw.TextStyle(
                            color: transaction.type == 'income'
                                ? PdfColors.green
                                : PdfColors.red,
                          ),
                        ),
                      ),
                    ],
                  );
                }).toList(),
              ],
            ),
          ];
        },
      ),
    );
    
    return await pdf.save();
  }

  /// Generate Excel statement
  static Future<Uint8List> _generateExcel({
    required List<Transaction> transactions,
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    final excel = Excel.createExcel();
    final sheet = excel['Statement'];
    
    // Remove default sheet
    excel.delete('Sheet1');
    
    final dateFormatter = DateFormat('dd/MM/yyyy');
    
    // Header
    sheet.cell(CellIndex.indexByString('A1')).value = TextCellValue('FinTrack Statement');
    sheet.cell(CellIndex.indexByString('A2')).value = TextCellValue('Period: ${dateFormatter.format(startDate)} - ${dateFormatter.format(endDate)}');
    sheet.cell(CellIndex.indexByString('A3')).value = TextCellValue('Generated: ${DateFormat('dd/MM/yyyy HH:mm').format(DateTime.now())}');
    
    // Calculate totals
    double totalIncome = 0;
    double totalExpense = 0;
    
    for (final transaction in transactions) {
      if (transaction.type == 'income') {
        totalIncome += transaction.amount;
      } else {
        totalExpense += transaction.amount;
      }
    }
    
    // Summary
    sheet.cell(CellIndex.indexByString('A5')).value = TextCellValue('Summary');
    sheet.cell(CellIndex.indexByString('A6')).value = TextCellValue('Total Income:');
    sheet.cell(CellIndex.indexByString('B6')).value = DoubleCellValue(totalIncome);
    sheet.cell(CellIndex.indexByString('A7')).value = TextCellValue('Total Expense:');
    sheet.cell(CellIndex.indexByString('B7')).value = DoubleCellValue(totalExpense);
    sheet.cell(CellIndex.indexByString('A8')).value = TextCellValue('Net Balance:');
    sheet.cell(CellIndex.indexByString('B8')).value = DoubleCellValue(totalIncome - totalExpense);
    
    // Transaction headers
    int startRow = 10;
    sheet.cell(CellIndex.indexByString('A$startRow')).value = TextCellValue('Date');
    sheet.cell(CellIndex.indexByString('B$startRow')).value = TextCellValue('Category');
    sheet.cell(CellIndex.indexByString('C$startRow')).value = TextCellValue('Account');
    sheet.cell(CellIndex.indexByString('D$startRow')).value = TextCellValue('Type');
    sheet.cell(CellIndex.indexByString('E$startRow')).value = TextCellValue('Amount');
    sheet.cell(CellIndex.indexByString('F$startRow')).value = TextCellValue('Description');
    
    // Transaction data
    for (int i = 0; i < transactions.length; i++) {
      final transaction = transactions[i];
      final row = startRow + 1 + i;
      
      sheet.cell(CellIndex.indexByString('A$row')).value = TextCellValue(dateFormatter.format(transaction.date));
      sheet.cell(CellIndex.indexByString('B$row')).value = TextCellValue(transaction.category);
      sheet.cell(CellIndex.indexByString('C$row')).value = TextCellValue(transaction.account);
      sheet.cell(CellIndex.indexByString('D$row')).value = TextCellValue(transaction.type.toUpperCase());
      sheet.cell(CellIndex.indexByString('E$row')).value = DoubleCellValue(transaction.amount);
      sheet.cell(CellIndex.indexByString('F$row')).value = TextCellValue(transaction.description ?? '');
    }
    
    return Uint8List.fromList(excel.encode()!);
  }

  /// Download statement file with improved error handling
  static Future<bool> downloadStatement({
    required BuildContext context,
    required List<Transaction> transactions,
    required DateTime startDate,
    required DateTime endDate,
    required String format,
  }) async {
    try {
      print('Starting download process for format: $format');

      // Validate inputs
      if (transactions.isEmpty) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('No transactions to export'),
              backgroundColor: Colors.orange,
            ),
          );
        }
        return false;
      }

      // Request permissions with retry logic
      bool hasPermission = false;
      int permissionRetries = 0;
      const maxRetries = 3;

      while (!hasPermission && permissionRetries < maxRetries) {
        try {
          hasPermission = await _requestPermissions();
          if (!hasPermission) {
            permissionRetries++;
            if (permissionRetries < maxRetries) {
              await Future.delayed(const Duration(milliseconds: 500));
            }
          }
        } catch (e) {
          print('Permission request attempt $permissionRetries failed: $e');
          permissionRetries++;
          if (permissionRetries < maxRetries) {
            await Future.delayed(const Duration(milliseconds: 500));
          }
        }
      }

      if (!hasPermission) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Storage permission is required to download files. Please grant permission in app settings.'),
              backgroundColor: Colors.red,
              duration: Duration(seconds: 5),
            ),
          );
        }
        return false;
      }

      print('Permissions granted, generating file content...');

      // Generate filename with timestamp to avoid conflicts
      final dateFormatter = DateFormat('yyyy-MM-dd');
      final timeFormatter = DateFormat('HHmmss');
      final timestamp = timeFormatter.format(DateTime.now());
      final filename = 'FinTrack_Statement_${dateFormatter.format(startDate)}_to_${dateFormatter.format(endDate)}_$timestamp.$format';

      // Generate file content
      Uint8List fileBytes;
      try {
        if (format == 'pdf') {
          print('Generating PDF...');
          fileBytes = await _generatePDF(
            transactions: transactions,
            startDate: startDate,
            endDate: endDate,
          );
        } else {
          print('Generating Excel...');
          fileBytes = await _generateExcel(
            transactions: transactions,
            startDate: startDate,
            endDate: endDate,
          );
        }
        print('File content generated successfully, size: ${fileBytes.length} bytes');
      } catch (e) {
        print('Error generating file content: $e');
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error generating $format file: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
        return false;
      }

      // Get downloads directory
      Directory downloadsDir;
      try {
        downloadsDir = await _getDownloadsDirectory();
        print('Downloads directory: ${downloadsDir.path}');
      } catch (e) {
        print('Error getting downloads directory: $e');
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error accessing downloads folder: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
        return false;
      }

      // Write file
      final file = File('${downloadsDir.path}/$filename');
      try {
        await file.writeAsBytes(fileBytes);
        print('File written successfully: ${file.path}');

        // Verify file was written
        if (!await file.exists()) {
          throw Exception('File was not created successfully');
        }

        final fileSize = await file.length();
        if (fileSize == 0) {
          throw Exception('File was created but is empty');
        }

        print('File verification successful, size: $fileSize bytes');
      } catch (e) {
        print('Error writing file: $e');
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error saving file: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
        return false;
      }

      // Show success message with option to open
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Statement saved: $filename'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 5),
            action: SnackBarAction(
              label: 'Open',
              textColor: Colors.white,
              onPressed: () async {
                try {
                  final result = await OpenFile.open(file.path);
                  print('Open file result: ${result.message}');
                } catch (e) {
                  print('Error opening file: $e');
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('File saved to: ${file.path}'),
                        backgroundColor: Colors.blue,
                        duration: const Duration(seconds: 5),
                      ),
                    );
                  }
                }
              },
            ),
          ),
        );
      }

      print('Download completed successfully');
      return true;
    } catch (e) {
      print('Unexpected error in downloadStatement: $e');
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error downloading statement: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
      return false;
    }
  }
}
