import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../core/app_export.dart';
import '../../widgets/offline_indicator.dart';
import '../../widgets/manual_sync_button.dart';
import '../../widgets/skeleton_loading.dart';
import '../../widgets/ads/banner_ad_widget.dart';
import './widgets/balance_card_widget.dart';
import './widgets/income_expense_cards_widget.dart';

import './widgets/quick_actions_widget.dart';
import './widgets/recent_transactions_widget.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({Key? key}) : super(key: key);

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen>
    with TickerProviderStateMixin {
  bool _isBalanceVisible = true;

  int _currentIndex = 0;
  late AnimationController _refreshController;
  late Animation<double> _refreshAnimation;

  late final FinancialDataManager _financialDataManager;
  bool _isInitialized = false;
  bool _hasData = false;


  @override
  void initState() {
    super.initState();
    _refreshController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _refreshAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _refreshController, curve: Curves.easeInOut),
    );

    _financialDataManager = FinancialDataManager.instance;
    _initializeData();
  }

  /// Initialize financial data with optimized loading
  Future<void> _initializeData() async {
    if (!_isInitialized) {
      // Listen for data updates from background loading
      _financialDataManager.addListener(_onDataUpdated);

      // Check if data is already available (from splash screen preloading)
      if (_financialDataManager.hasAnyData) {
        if (mounted) {
          setState(() {
            _isInitialized = true;
            _hasData = true;
          });
        }
      } else {
        // Start initialization if not already started
        _financialDataManager.initialize().then((_) {
          if (mounted) {
            setState(() {
              _isInitialized = true;
              _hasData = _financialDataManager.hasAnyData;
            });
          }
        }).catchError((error) {
          debugPrint('Dashboard data initialization error: $error');
          if (mounted) {
            setState(() {
              _isInitialized = true;
              _hasData = _financialDataManager.hasAnyData;
            });
          }
        });
      }
    }
  }

  /// Called when financial data is updated
  void _onDataUpdated() {
    if (mounted) {
      setState(() {
        _hasData = _financialDataManager.hasAnyData;
      });
    }
  }

  @override
  void dispose() {
    _refreshController.dispose();
    _financialDataManager.removeListener(_onDataUpdated);
    super.dispose();
  }

  /// Get total balance from financial data manager
  double get _totalBalance => _financialDataManager.totalBalance;

  /// Get monthly income from financial data manager
  double get _totalIncome => _financialDataManager.monthlyIncome;

  /// Get monthly expense from financial data manager
  double get _totalExpense => _financialDataManager.monthlyExpense;

  /// Get recent transactions from financial data manager
  List<Transaction> get _recentTransactions => _financialDataManager.recentTransactions;

  /// Convert Transaction objects to Map format for compatibility with existing widgets
  List<Map<String, dynamic>> get _transactionsAsMap {
    return _recentTransactions.map((transaction) => {
      'id': transaction.id,
      'type': transaction.type,
      'amount': transaction.amount,
      'category': transaction.category,
      'description': transaction.displayDescription,
      'account': transaction.account,
      'date': transaction.date,
    }).toList();
  }

  Future<void> _onRefresh() async {
    _refreshController.forward();

    try {
      // Manual sync trigger
      final syncService = DataSyncService.instance;
      await syncService.forceSyncNow();

      // Refresh financial data
      await _financialDataManager.refreshAll();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Data synced successfully'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      debugPrint('Manual sync error: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Sync failed: ${e.toString()}'),
            backgroundColor: AppTheme.errorRed,
          ),
        );
      }
    } finally {
      _refreshController.reset();
    }
  }

  void _onBottomNavTap(int index) {
    setState(() {
      _currentIndex = index;
    });

    switch (index) {
      case 0:
        // Already on Dashboard
        break;
      case 1:
        Navigator.pushNamed(context, '/transaction-history-screen');
        break;
      case 2:
        Navigator.pushNamed(context, '/coming-soon-accounts');
        break;
      case 3:
        Navigator.pushNamed(context, '/reports-screen');
        break;
      case 4:
        Navigator.pushNamed(context, '/settings-screen');
        break;
    }
  }

  void _editTransaction(Map<String, dynamic> transaction) {
    Navigator.pushNamed(context, '/add-transaction-screen',
        arguments: transaction);
  }

  Future<void> _deleteTransaction(Map<String, dynamic> transaction) async {
    final transactionId = transaction['id'] as int?;
    if (transactionId == null) return;

    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Transaction'),
        content: const Text('Are you sure you want to delete this transaction?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final success = await _financialDataManager.deleteTransaction(transactionId);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(success
                ? 'Transaction deleted successfully'
                : 'Failed to delete transaction'),
            backgroundColor: success ? Colors.green : Colors.red,
          ),
        );
      }
    }
  }

  /// Refresh all financial data with manual sync
  Future<void> _refreshData() async {
    _refreshController.forward();

    try {
      // Manual sync trigger
      final syncService = DataSyncService.instance;
      await syncService.forceSyncNow();

      // Refresh financial data
      await _financialDataManager.refreshAll();
    } catch (e) {
      debugPrint('Refresh error: $e');
    } finally {
      _refreshController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return OfflineIndicator(
      child: ListenableBuilder(
        listenable: Listenable.merge([_financialDataManager, FormattingService.instance]),
        builder: (context, child) {
          return Scaffold(
      backgroundColor:
          isDark ? AppTheme.backgroundDark : AppTheme.backgroundLight,
      appBar: AppBar(
        title: Row(
          children: [
            Container(
              padding: EdgeInsets.all(2.w),
              decoration: BoxDecoration(
                gradient: AppTheme.primaryGradient,
                borderRadius: BorderRadius.circular(8),
              ),
              child: CustomIconWidget(
                iconName: 'account_balance_wallet',
                color: Colors.white,
                size: 24,
              ),
            ),
            SizedBox(width: 3.w),
            Text(
              'FinTrack',
              style: GoogleFonts.inter(
                fontSize: 20.sp,
                fontWeight: FontWeight.w700,
                color: isDark
                    ? AppTheme.textHighEmphasisDark
                    : AppTheme.textHighEmphasisLight,
              ),
            ),
          ],
        ),
        actions: [
          const CompactOfflineStatus(),
          const SyncStatusIndicator(),
          ManualSyncButton(
            onPressed: _onRefresh,
            rotationController: _refreshController,
          ),
          SizedBox(width: 2.w),
        ],
        elevation: 0,
        backgroundColor:
            isDark ? AppTheme.backgroundDark : AppTheme.backgroundLight,
      ),
      body: RefreshIndicator(
        onRefresh: _onRefresh,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: 1.h),

              SizedBox(height: 1.h),

              // Balance Card - Smooth transition from skeleton to data
              AnimatedSwitcher(
                duration: const Duration(milliseconds: 600),
                transitionBuilder: (Widget child, Animation<double> animation) {
                  return FadeTransition(
                    opacity: animation,
                    child: SlideTransition(
                      position: Tween<Offset>(
                        begin: const Offset(0, 0.1),
                        end: Offset.zero,
                      ).animate(CurvedAnimation(
                        parent: animation,
                        curve: Curves.easeOutCubic,
                      )),
                      child: child,
                    ),
                  );
                },
                child: _hasData
                    ? BalanceCardWidget(
                        key: const ValueKey('balance_card'),
                        totalBalance: _totalBalance,
                        isBalanceVisible: _isBalanceVisible,
                        onToggleVisibility: () {
                          setState(() {
                            _isBalanceVisible = !_isBalanceVisible;
                          });
                        },
                      )
                    : const BalanceCardSkeleton(
                        key: ValueKey('balance_skeleton'),
                      ),
              ),

              SizedBox(height: 2.h),

              // Income/Expense Cards - Smooth transition from skeleton to data
              AnimatedSwitcher(
                duration: const Duration(milliseconds: 700),
                transitionBuilder: (Widget child, Animation<double> animation) {
                  return FadeTransition(
                    opacity: animation,
                    child: SlideTransition(
                      position: Tween<Offset>(
                        begin: const Offset(0, 0.1),
                        end: Offset.zero,
                      ).animate(CurvedAnimation(
                        parent: animation,
                        curve: Curves.easeOutCubic,
                      )),
                      child: child,
                    ),
                  );
                },
                child: _hasData
                    ? IncomeExpenseCardsWidget(
                        key: const ValueKey('income_expense_cards'),
                        totalIncome: _totalIncome,
                        totalExpense: _totalExpense,
                        incomePercentageChange: 12.5,
                        expensePercentageChange: -8.3,
                      )
                    : const IncomeExpenseCardsSkeleton(
                        key: ValueKey('income_expense_skeleton'),
                      ),
              ),

              SizedBox(height: 3.h),

              // Quick Actions
              QuickActionsWidget(
                onAddTransaction: () {
                  Navigator.pushNamed(context, '/add-transaction-screen');
                },
                onManageAccounts: () {
                  // Show Coming Soon screen for now
                  Navigator.pushNamed(context, '/coming-soon-accounts');
                },
                onViewReports: () {
                  Navigator.pushNamed(context, '/reports-screen');
                },
              ),

              SizedBox(height: 3.h),

              // Recent Transactions - Smooth transition from skeleton to data
              AnimatedSwitcher(
                duration: const Duration(milliseconds: 800),
                transitionBuilder: (Widget child, Animation<double> animation) {
                  return FadeTransition(
                    opacity: animation,
                    child: SlideTransition(
                      position: Tween<Offset>(
                        begin: const Offset(0, 0.1),
                        end: Offset.zero,
                      ).animate(CurvedAnimation(
                        parent: animation,
                        curve: Curves.easeOutCubic,
                      )),
                      child: child,
                    ),
                  );
                },
                child: _hasData
                    ? RecentTransactionsWidget(
                        key: const ValueKey('recent_transactions'),
                        transactions: _transactionsAsMap,
                        onViewAll: () {
                          Navigator.pushNamed(context, '/transaction-history-screen');
                        },
                        onEditTransaction: _editTransaction,
                        onDeleteTransaction: _deleteTransaction,
                      )
                    : const RecentTransactionsSkeleton(
                        key: ValueKey('recent_transactions_skeleton'),
                      ),
              ),

              SizedBox(height: 10.h), // Bottom padding for FAB
            ],
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.pushNamed(context, '/add-transaction-screen');
        },
        child: CustomIconWidget(
          iconName: 'add',
          color: Colors.white,
          size: 28,
        ),
      ),
      bottomNavigationBar: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const BannerAdWidget(),
          BottomNavigationBar(
        currentIndex: _currentIndex,
        onTap: _onBottomNavTap,
        type: BottomNavigationBarType.fixed,
        backgroundColor:
            isDark ? AppTheme.cardSurfaceDark : AppTheme.cardSurface,
        selectedItemColor: AppTheme.primaryTeal,
        unselectedItemColor: AppTheme.neutralGray,
        items: [
          BottomNavigationBarItem(
            icon: CustomIconWidget(
              iconName: 'home',
              color: _currentIndex == 0
                  ? AppTheme.primaryTeal
                  : AppTheme.neutralGray,
              size: 24,
            ),
            label: 'Home',
          ),
          BottomNavigationBarItem(
            icon: CustomIconWidget(
              iconName: 'receipt_long',
              color: _currentIndex == 1
                  ? AppTheme.primaryTeal
                  : AppTheme.neutralGray,
              size: 24,
            ),
            label: 'Transactions',
          ),
          BottomNavigationBarItem(
            icon: CustomIconWidget(
              iconName: 'account_balance',
              color: _currentIndex == 2
                  ? AppTheme.primaryTeal
                  : AppTheme.neutralGray,
              size: 24,
            ),
            label: 'Accounts',
          ),
          BottomNavigationBarItem(
            icon: CustomIconWidget(
              iconName: 'bar_chart',
              color: _currentIndex == 3
                  ? AppTheme.primaryTeal
                  : AppTheme.neutralGray,
              size: 24,
            ),
            label: 'Reports',
          ),
          BottomNavigationBarItem(
            icon: CustomIconWidget(
              iconName: 'settings',
              color: _currentIndex == 4
                  ? AppTheme.primaryTeal
                  : AppTheme.neutralGray,
              size: 24,
            ),
            label: 'Settings',
          ),
        ],
      ),
        ],
      ),
        );
      },
      ),
    );
  }
}
