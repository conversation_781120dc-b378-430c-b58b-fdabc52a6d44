# AdMob Rewarded Ad Integration Guide for Flutter FinTrack

## Overview
This guide provides step-by-step instructions for implementing rewarded ad functionality in any screen of the Flutter FinTrack application. Rewarded ads allow users to earn rewards (like premium features, downloads, etc.) by watching video advertisements.

## Prerequisites
- Google AdMob account with app registered
- Real ad unit IDs for production (currently using test IDs)
- `google_mobile_ads` package already integrated

## Current Ad Service Architecture

### Core Files
1. **`lib/core/services/ad_service.dart`** - Main ad management service
2. **`lib/widgets/ads/banner_ad_widget.dart`** - Banner ad widget
3. **`lib/widgets/ads/native_ad_widget.dart`** - Native ad widget

## Step-by-Step Implementation Guide

### Step 1: Import Required Dependencies

In any screen where you want to add rewarded ads, add these imports:

```dart
import '../../core/services/ad_service.dart';
import 'package:sizer/sizer.dart'; // For responsive sizing (if not already imported)
```

### Step 2: Basic Rewarded Ad Implementation

#### Simple Rewarded Ad Call
```dart
void _showRewardedAd() {
  AdService().showRewardedAd(() {
    // Reward callback - executed when user completes the ad
    _grantReward();
  });
}

void _grantReward() {
  // Implement your reward logic here
  ScaffoldMessenger.of(context).showSnackBar(
    SnackBar(
      content: Text('Reward granted! You unlocked premium feature.'),
      backgroundColor: AppTheme.successGreen,
      behavior: SnackBarBehavior.floating,
    ),
  );
}
```

#### Advanced Rewarded Ad with Loading Dialog
```dart
void _showRewardedAdWithLoading() {
  // Show loading dialog
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (context) => AlertDialog(
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          CircularProgressIndicator(color: AppTheme.primaryTeal),
          SizedBox(height: 2.h),
          Text(
            'Loading ad...\nPlease watch the ad to unlock this feature',
            textAlign: TextAlign.center,
            style: AppTheme.lightTheme.textTheme.bodyMedium,
          ),
        ],
      ),
    ),
  );

  // Show rewarded ad
  AdService().showRewardedAd(() {
    // User completed the ad
    if (mounted) {
      Navigator.pop(context); // Close loading dialog
      _grantReward();
    }
  });

  // Auto-close loading dialog after timeout
  Future.delayed(const Duration(seconds: 5), () {
    if (mounted && Navigator.canPop(context)) {
      Navigator.pop(context);
      // Optionally still grant reward if ad fails to load
      _grantReward();
    }
  });
}
```

### Step 3: Integration Examples

#### Example 1: Premium Feature Unlock
```dart
class PremiumFeatureButton extends StatelessWidget {
  final String featureName;
  final VoidCallback onUnlock;

  const PremiumFeatureButton({
    Key? key,
    required this.featureName,
    required this.onUnlock,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ElevatedButton.icon(
      onPressed: () => _showRewardedAdForFeature(context),
      style: ElevatedButton.styleFrom(
        backgroundColor: AppTheme.primaryTeal,
        foregroundColor: Colors.white,
      ),
      icon: Icon(Icons.play_circle_filled),
      label: Text('Watch Ad to Unlock $featureName'),
    );
  }

  void _showRewardedAdForFeature(BuildContext context) {
    AdService().showRewardedAd(() {
      onUnlock();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('$featureName unlocked!'),
          backgroundColor: AppTheme.successGreen,
        ),
      );
    });
  }
}
```

#### Example 2: Download with Rewarded Ad (Like Statement Download)
```dart
void _downloadWithRewardedAd() {
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (context) => AlertDialog(
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          CircularProgressIndicator(color: AppTheme.primaryTeal),
          SizedBox(height: 2.h),
          Text(
            'Loading ad...\nPlease watch the ad to download your file',
            textAlign: TextAlign.center,
            style: AppTheme.lightTheme.textTheme.bodyMedium,
          ),
        ],
      ),
    ),
  );

  AdService().showRewardedAd(() {
    if (mounted) {
      Navigator.pop(context); // Close loading dialog
      _proceedWithDownload();
    }
  });

  // Fallback timeout
  Future.delayed(const Duration(seconds: 5), () {
    if (mounted && Navigator.canPop(context)) {
      Navigator.pop(context);
      _proceedWithDownload(); // Allow download even if ad fails
    }
  });
}

void _proceedWithDownload() {
  // Your download logic here
  print('Starting download...');
}
```

### Step 4: UI Integration Patterns

#### Pattern 1: Replace Existing Button
```dart
// Before (regular button)
ElevatedButton(
  onPressed: _downloadFile,
  child: Text('Download'),
)

// After (with rewarded ad)
ElevatedButton.icon(
  onPressed: _downloadWithRewardedAd,
  icon: Icon(Icons.play_circle_filled),
  label: Text('Watch Ad to Download'),
)
```

#### Pattern 2: Add Premium Option
```dart
Column(
  children: [
    // Regular option
    ElevatedButton(
      onPressed: _basicFeature,
      child: Text('Basic Feature'),
    ),
    
    SizedBox(height: 1.h),
    
    // Premium option with ad
    OutlinedButton.icon(
      onPressed: _showRewardedAdForPremium,
      icon: Icon(Icons.star),
      label: Text('Watch Ad for Premium'),
    ),
  ],
)
```

### Step 5: Production Setup - Replacing Test Ad Units

#### Current Test Ad Units (in `lib/core/services/ad_service.dart`)
```dart
// TEST AD UNITS - REPLACE THESE FOR PRODUCTION
static const String _bannerAdUnitId = 'ca-app-pub-3940256099942544/6300978111';
static const String _nativeAdUnitId = 'ca-app-pub-3940256099942544/2247696110';
static const String _rewardedAdUnitId = 'ca-app-pub-3940256099942544/5224354917';
```

#### Production Ad Unit Setup
1. **Get Real Ad Unit IDs from AdMob Console:**
   - Go to [AdMob Console](https://apps.admob.com/)
   - Select your app
   - Create new ad units for each type needed
   - Copy the ad unit IDs

2. **Replace Test IDs in `ad_service.dart`:**
```dart
// PRODUCTION AD UNITS - REPLACE WITH YOUR REAL IDS
static const String _bannerAdUnitId = 'ca-app-pub-XXXXXXXXXXXXXXXX/XXXXXXXXXX';
static const String _nativeAdUnitId = 'ca-app-pub-XXXXXXXXXXXXXXXX/XXXXXXXXXX';
static const String _rewardedAdUnitId = 'ca-app-pub-XXXXXXXXXXXXXXXX/XXXXXXXXXX';
```

3. **Update App ID in `android/app/src/main/AndroidManifest.xml`:**
```xml
<meta-data
    android:name="com.google.android.gms.ads.APPLICATION_ID"
    android:value="ca-app-pub-XXXXXXXXXXXXXXXX~XXXXXXXXXX"/>
```

### Step 6: Advanced Rewarded Ad Features

#### Minimum Watch Time Implementation
```dart
class RewardedAdWithTimer {
  static const int minimumWatchTimeSeconds = 30;
  Timer? _watchTimer;
  bool _hasWatchedMinimumTime = false;

  void showRewardedAdWithMinimumTime(VoidCallback onReward) {
    _hasWatchedMinimumTime = false;

    // Start timer
    _watchTimer = Timer(Duration(seconds: minimumWatchTimeSeconds), () {
      _hasWatchedMinimumTime = true;
    });

    AdService().showRewardedAd(() {
      _watchTimer?.cancel();
      if (_hasWatchedMinimumTime) {
        onReward();
      } else {
        // Show message that user needs to watch longer
        _showMinimumTimeMessage();
      }
    });
  }

  void _showMinimumTimeMessage() {
    // Show snackbar or dialog explaining minimum watch time
  }
}
```

#### Frequency Capping (Limit Ads Per Day)
```dart
class AdFrequencyManager {
  static const String _rewardedAdCountKey = 'rewarded_ad_count';
  static const String _lastAdDateKey = 'last_ad_date';
  static const int maxAdsPerDay = 5;

  static Future<bool> canShowRewardedAd() async {
    final prefs = await SharedPreferences.getInstance();
    final today = DateTime.now().toIso8601String().split('T')[0];
    final lastAdDate = prefs.getString(_lastAdDateKey) ?? '';

    if (lastAdDate != today) {
      // New day, reset counter
      await prefs.setString(_lastAdDateKey, today);
      await prefs.setInt(_rewardedAdCountKey, 0);
      return true;
    }

    final adCount = prefs.getInt(_rewardedAdCountKey) ?? 0;
    return adCount < maxAdsPerDay;
  }

  static Future<void> incrementAdCount() async {
    final prefs = await SharedPreferences.getInstance();
    final currentCount = prefs.getInt(_rewardedAdCountKey) ?? 0;
    await prefs.setInt(_rewardedAdCountKey, currentCount + 1);
  }
}
```

### Step 7: Error Handling and Fallbacks

#### Robust Rewarded Ad Implementation
```dart
void _showRewardedAdWithFallback() async {
  // Check frequency cap
  if (!await AdFrequencyManager.canShowRewardedAd()) {
    _showAdLimitMessage();
    return;
  }

  // Show loading
  _showLoadingDialog();

  try {
    AdService().showRewardedAd(() async {
      // User completed ad
      await AdFrequencyManager.incrementAdCount();
      if (mounted) {
        Navigator.pop(context); // Close loading
        _grantReward();
      }
    });

    // Timeout fallback
    Future.delayed(const Duration(seconds: 10), () {
      if (mounted && Navigator.canPop(context)) {
        Navigator.pop(context);
        _showAdFailedDialog();
      }
    });
  } catch (e) {
    if (mounted) {
      Navigator.pop(context);
      _showAdFailedDialog();
    }
  }
}

void _showAdLimitMessage() {
  ScaffoldMessenger.of(context).showSnackBar(
    SnackBar(
      content: Text('Daily ad limit reached. Try again tomorrow!'),
      backgroundColor: AppTheme.warningOrange,
    ),
  );
}

void _showAdFailedDialog() {
  showDialog(
    context: context,
    builder: (context) => AlertDialog(
      title: Text('Ad Not Available'),
      content: Text('Unable to load ad. Would you like to proceed anyway?'),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            Navigator.pop(context);
            _grantReward(); // Allow action without ad
          },
          child: Text('Continue'),
        ),
      ],
    ),
  );
}
```

### Step 8: Testing and Debugging

#### Debug Mode Setup
```dart
// Add to ad_service.dart for debugging
static bool get isDebugMode => kDebugMode;

void showRewardedAd(VoidCallback onReward) {
  if (isDebugMode) {
    print('🎬 DEBUG: Showing rewarded ad');
    // In debug mode, immediately grant reward for testing
    Future.delayed(Duration(seconds: 2), onReward);
    return;
  }

  // Production ad logic
  _loadAndShowRewardedAd(onReward);
}
```

#### Testing Checklist
- [ ] Test with test ad units first
- [ ] Verify ad loading and display
- [ ] Test reward callback execution
- [ ] Test error handling (no internet, ad failed to load)
- [ ] Test frequency capping
- [ ] Test minimum watch time
- [ ] Verify production ad units work
- [ ] Test on different devices and Android versions

### Step 9: Common Integration Points

#### Reports Screen (Already Implemented)
Location: `lib/presentation/reports_screen/reports_screen.dart`
- Banner ad below report period selection

#### Transaction History (Native Ads)
Location: `lib/presentation/dashboard_screen/widgets/recent_transactions_widget.dart`
- Native ads every 3-4 transactions

#### Statement Downloads (Rewarded Ads)
Location: `lib/presentation/reports_screen/reports_screen.dart`
- Rewarded ad before PDF/Excel downloads

### Step 10: Performance Optimization

#### Preload Rewarded Ads
```dart
// In your screen's initState()
@override
void initState() {
  super.initState();
  // Preload rewarded ad for better user experience
  AdService().preloadRewardedAd();
}
```

#### Memory Management
```dart
@override
void dispose() {
  // Clean up any ad-related resources
  AdService().disposeRewardedAd();
  super.dispose();
}
```

## Summary

This guide covers complete rewarded ad integration for the Flutter FinTrack app. Key points:

1. **Simple Integration**: Use `AdService().showRewardedAd(callback)` for basic implementation
2. **Production Setup**: Replace test ad unit IDs with real ones from AdMob Console
3. **Advanced Features**: Implement minimum watch time, frequency capping, and error handling
4. **Testing**: Always test thoroughly with test ads before production
5. **User Experience**: Provide loading states, fallbacks, and clear messaging

For any issues or questions, refer to the [Google Mobile Ads Flutter documentation](https://developers.google.com/admob/flutter/quick-start).
