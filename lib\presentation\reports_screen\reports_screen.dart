import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:fl_chart/fl_chart.dart';

import '../../core/app_export.dart';
import '../../core/services/financial_data_manager.dart';
import '../../core/services/ad_service.dart';
import '../../core/services/file_download_service.dart';
import '../../widgets/ads/banner_ad_widget.dart';

class ReportsScreen extends StatefulWidget {
  const ReportsScreen({Key? key}) : super(key: key);

  @override
  State<ReportsScreen> createState() => _ReportsScreenState();
}

class _ReportsScreenState extends State<ReportsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final FinancialDataManager _dataManager = FinancialDataManager.instance;
  
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();
  
  Map<String, dynamic> _reportData = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadReportData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadReportData() async {
    setState(() => _isLoading = true);
    
    try {
      await _dataManager.initialize();
      
      // Calculate report data based on current period
      final transactions = _dataManager.transactions;
      final filteredTransactions = transactions.where((t) {
        final transactionDate = t.date;
        return transactionDate.isAfter(_startDate.subtract(const Duration(days: 1))) &&
               transactionDate.isBefore(_endDate.add(const Duration(days: 1)));
      }).toList();

      double totalIncome = 0;
      double totalExpense = 0;
      Map<String, double> categoryExpenses = {};
      Map<String, double> categoryIncomes = {};
      
      for (final transaction in filteredTransactions) {
        if (transaction.type == 'income') {
          totalIncome += transaction.amount;
          categoryIncomes[transaction.category] = 
              (categoryIncomes[transaction.category] ?? 0) + transaction.amount;
        } else {
          totalExpense += transaction.amount;
          categoryExpenses[transaction.category] = 
              (categoryExpenses[transaction.category] ?? 0) + transaction.amount;
        }
      }

      setState(() {
        _reportData = {
          'totalIncome': totalIncome,
          'totalExpense': totalExpense,
          'netBalance': totalIncome - totalExpense,
          'categoryExpenses': categoryExpenses,
          'categoryIncomes': categoryIncomes,
          'transactions': filteredTransactions,
        };
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading report data: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return ListenableBuilder(
      listenable: FormattingService.instance,
      builder: (context, child) {
        return Scaffold(
      backgroundColor: isDark ? AppTheme.backgroundDark : AppTheme.backgroundLight,
      appBar: _buildAppBar(),
      body: _isLoading ? _buildLoadingState() : _buildContent(isDark),
        );
      },
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      title: Text(
        'Financial Reports',
        style: AppTheme.lightTheme.textTheme.headlineSmall?.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ),
      centerTitle: true,
      actions: [
        IconButton(
          onPressed: _showDownloadOptions,
          icon: CustomIconWidget(
            iconName: 'download',
            color: AppTheme.primaryTeal,
            size: 24,
          ),
        ),
        IconButton(
          onPressed: _showDateRangePicker,
          icon: CustomIconWidget(
            iconName: 'date_range',
            color: AppTheme.primaryTeal,
            size: 24,
          ),
        ),
      ],
      bottom: TabBar(
        controller: _tabController,
        labelColor: AppTheme.primaryTeal,
        unselectedLabelColor: AppTheme.textMediumEmphasisLight,
        indicatorColor: AppTheme.primaryTeal,
        tabs: const [
          Tab(text: 'Overview'),
          Tab(text: 'Categories'),
          Tab(text: 'Trends'),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: CircularProgressIndicator(),
    );
  }

  Widget _buildContent(bool isDark) {
    return Column(
      children: [
        _buildDateRangeHeader(isDark),

        // Banner Ad below Report Period selection
        const BannerAdWidget(),

        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildOverviewTab(isDark),
              _buildCategoriesTab(isDark),
              _buildTrendsTab(isDark),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDateRangeHeader(bool isDark) {
    return Container(
      margin: EdgeInsets.all(4.w),
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: isDark ? AppTheme.cardSurfaceDark : AppTheme.cardSurface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.borderSubtle),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Report Period',
                style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: 1.h),
              Text(
                '${_formatDate(_startDate)} - ${_formatDate(_endDate)}',
                style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                  color: AppTheme.textMediumEmphasisLight,
                ),
              ),
            ],
          ),
          ElevatedButton(
            onPressed: _showDateRangePicker,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryTeal,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text('Change'),
          ),
        ],
      ),
    );
  }

  Widget _buildOverviewTab(bool isDark) {
    final totalIncome = _reportData['totalIncome'] ?? 0.0;
    final totalExpense = _reportData['totalExpense'] ?? 0.0;
    final netBalance = _reportData['netBalance'] ?? 0.0;

    return SingleChildScrollView(
      padding: EdgeInsets.all(4.w),
      child: Column(
        children: [
          // Summary Cards
          Row(
            children: [
              Expanded(
                child: _buildSummaryCard(
                  'Total Income',
                  FormattingService.instance.formatCurrency(totalIncome),
                  AppTheme.successGreen,
                  'trending_up',
                  isDark,
                ),
              ),
              SizedBox(width: 3.w),
              Expanded(
                child: _buildSummaryCard(
                  'Total Expense',
                  FormattingService.instance.formatCurrency(totalExpense),
                  AppTheme.errorRed,
                  'trending_down',
                  isDark,
                ),
              ),
            ],
          ),
          
          SizedBox(height: 3.h),
          
          // Net Balance Card
          _buildNetBalanceCard(netBalance, isDark),
          
          SizedBox(height: 3.h),
          
          // Income vs Expense Chart
          _buildIncomeExpenseChart(totalIncome, totalExpense, isDark),
        ],
      ),
    );
  }

  Widget _buildCategoriesTab(bool isDark) {
    final categoryExpenses = _reportData['categoryExpenses'] as Map<String, double>? ?? {};
    final categoryIncomes = _reportData['categoryIncomes'] as Map<String, double>? ?? {};

    return SingleChildScrollView(
      padding: EdgeInsets.all(4.w),
      child: Column(
        children: [
          // Expense Categories
          _buildCategorySection('Expense Categories', categoryExpenses, AppTheme.errorRed, isDark),
          
          SizedBox(height: 4.h),
          
          // Income Categories
          _buildCategorySection('Income Categories', categoryIncomes, AppTheme.successGreen, isDark),
        ],
      ),
    );
  }

  Widget _buildTrendsTab(bool isDark) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(4.w),
      child: Column(
        children: [
          _buildTrendChart(isDark),
          SizedBox(height: 3.h),
          _buildTransactionsList(isDark),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(String title, String amount, Color color, String iconName, bool isDark) {
    return Container(
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: isDark ? AppTheme.cardSurfaceDark : AppTheme.cardSurface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              CustomIconWidget(
                iconName: iconName,
                color: color,
                size: 24,
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.5.h),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  title.split(' ')[0],
                  style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                    color: color,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 2.h),
          Text(
            title,
            style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
              color: AppTheme.textMediumEmphasisLight,
            ),
          ),
          SizedBox(height: 1.h),
          Text(
            amount,
            style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w700,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNetBalanceCard(double netBalance, bool isDark) {
    final isPositive = netBalance >= 0;
    final color = isPositive ? AppTheme.successGreen : AppTheme.errorRed;

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(5.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            color.withValues(alpha: 0.1),
            color.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          CustomIconWidget(
            iconName: isPositive ? 'account_balance_wallet' : 'warning',
            color: color,
            size: 32,
          ),
          SizedBox(height: 2.h),
          Text(
            'Net Balance',
            style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
              color: AppTheme.textMediumEmphasisLight,
            ),
          ),
          SizedBox(height: 1.h),
          Text(
            FormattingService.instance.formatCurrency(netBalance.abs()),
            style: AppTheme.lightTheme.textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.w700,
              color: color,
            ),
          ),
          SizedBox(height: 1.h),
          Text(
            isPositive ? 'Surplus' : 'Deficit',
            style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
              color: color,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildIncomeExpenseChart(double income, double expense, bool isDark) {
    if (income == 0 && expense == 0) {
      return _buildNoDataCard('No transactions in selected period', isDark);
    }

    return Container(
      height: 40.h,
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: isDark ? AppTheme.cardSurfaceDark : AppTheme.cardSurface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.borderSubtle),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Income vs Expense',
            style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 3.h),
          Expanded(
            child: PieChart(
              PieChartData(
                sections: [
                  PieChartSectionData(
                    value: income,
                    color: AppTheme.successGreen,
                    title: 'Income\n${FormattingService.instance.formatCurrency(income)}',
                    radius: 60,
                    titleStyle: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  PieChartSectionData(
                    value: expense,
                    color: AppTheme.errorRed,
                    title: 'Expense\n${FormattingService.instance.formatCurrency(expense)}',
                    radius: 60,
                    titleStyle: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
                centerSpaceRadius: 40,
                sectionsSpace: 2,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategorySection(String title, Map<String, double> categories, Color color, bool isDark) {
    if (categories.isEmpty) {
      return _buildNoDataCard('No $title data', isDark);
    }

    final sortedCategories = categories.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    return Container(
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: isDark ? AppTheme.cardSurfaceDark : AppTheme.cardSurface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.borderSubtle),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 3.h),
          ...sortedCategories.take(5).map((entry) => _buildCategoryItem(
            entry.key,
            entry.value,
            color,
            sortedCategories.first.value,
          )).toList(),
        ],
      ),
    );
  }

  Widget _buildCategoryItem(String category, double amount, Color color, double maxAmount) {
    final percentage = (amount / maxAmount * 100).clamp(0, 100);

    return Padding(
      padding: EdgeInsets.only(bottom: 2.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                category,
                style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                FormattingService.instance.formatCurrency(amount),
                style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: color,
                ),
              ),
            ],
          ),
          SizedBox(height: 1.h),
          LinearProgressIndicator(
            value: percentage / 100,
            backgroundColor: color.withValues(alpha: 0.1),
            valueColor: AlwaysStoppedAnimation<Color>(color),
            minHeight: 0.8.h,
          ),
        ],
      ),
    );
  }

  Widget _buildTrendChart(bool isDark) {
    return Container(
      height: 30.h,
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: isDark ? AppTheme.cardSurfaceDark : AppTheme.cardSurface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.borderSubtle),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Spending Trend',
            style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 2.h),
          Expanded(
            child: Center(
              child: Text(
                'Trend chart will be implemented with more transaction data',
                style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                  color: AppTheme.textMediumEmphasisLight,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionsList(bool isDark) {
    final transactions = _reportData['transactions'] as List? ?? [];

    if (transactions.isEmpty) {
      return _buildNoDataCard('No transactions in selected period', isDark);
    }

    return Container(
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: isDark ? AppTheme.cardSurfaceDark : AppTheme.cardSurface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.borderSubtle),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Recent Transactions',
            style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 2.h),
          ...transactions.take(5).map((transaction) => _buildTransactionItem(transaction)).toList(),
        ],
      ),
    );
  }

  Widget _buildTransactionItem(dynamic transaction) {
    final isIncome = transaction.type == 'income';
    final color = isIncome ? AppTheme.successGreen : AppTheme.errorRed;

    return Padding(
      padding: EdgeInsets.only(bottom: 2.h),
      child: Row(
        children: [
          Container(
            width: 10.w,
            height: 10.w,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(5.w),
            ),
            child: Center(
              child: CustomIconWidget(
                iconName: isIncome ? 'trending_up' : 'trending_down',
                color: color,
                size: 20,
              ),
            ),
          ),
          SizedBox(width: 3.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  transaction.category,
                  style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  _formatDate(transaction.date),
                  style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                    color: AppTheme.textMediumEmphasisLight,
                  ),
                ),
              ],
            ),
          ),
          Text(
            '${isIncome ? '+' : '-'}${FormattingService.instance.formatCurrency(transaction.amount)}',
            style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNoDataCard(String message, bool isDark) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(8.w),
      decoration: BoxDecoration(
        color: isDark ? AppTheme.cardSurfaceDark : AppTheme.cardSurface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.borderSubtle),
      ),
      child: Column(
        children: [
          CustomIconWidget(
            iconName: 'info',
            color: AppTheme.textMediumEmphasisLight,
            size: 32,
          ),
          SizedBox(height: 2.h),
          Text(
            message,
            style: AppTheme.lightTheme.textTheme.bodyLarge?.copyWith(
              color: AppTheme.textMediumEmphasisLight,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _showDateRangePicker() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: DateTimeRange(start: _startDate, end: _endDate),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: AppTheme.primaryTeal,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _startDate = picked.start;
        _endDate = picked.end;
      });
      _loadReportData();
    }
  }

  String _formatDate(DateTime date) {
    return FormattingService.instance.formatDate(date);
  }

  /// Show download options dialog
  void _showDownloadOptions() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _DownloadOptionsSheet(
        startDate: _startDate,
        endDate: _endDate,
        onDownload: _downloadStatement,
      ),
    );
  }

  /// Download statement with rewarded ad
  Future<void> _downloadStatement({
    required DateTime startDate,
    required DateTime endDate,
    required String format,
  }) async {
    // Show rewarded ad before download
    _showRewardedAdBeforeDownload(startDate, endDate, format);
  }

  /// Show rewarded ad before allowing download
  void _showRewardedAdBeforeDownload(DateTime startDate, DateTime endDate, String format) {
    // Show loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircularProgressIndicator(color: AppTheme.primaryTeal),
            SizedBox(height: 2.h),
            Text(
              'Loading ad...\nPlease watch the ad to download your file',
              textAlign: TextAlign.center,
              style: AppTheme.lightTheme.textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );

    // Show rewarded ad
    AdService().showRewardedAd(() {
      // User completed the ad, proceed with download
      if (mounted) {
        Navigator.pop(context); // Close loading dialog
        _proceedWithDownload(startDate, endDate, format);
      }
    });

    // Close loading dialog after a delay if ad doesn't show
    Future.delayed(const Duration(seconds: 5), () {
      if (mounted && Navigator.canPop(context)) {
        Navigator.pop(context);
        _proceedWithDownload(startDate, endDate, format);
      }
    });
  }

  /// Proceed with the actual download
  Future<void> _proceedWithDownload(DateTime startDate, DateTime endDate, String format) async {
    try {
      // Filter transactions by date range
      final transactions = _dataManager.transactions;
      final filteredTransactions = transactions.where((transaction) {
        final transactionDate = transaction.date;
        return transactionDate.isAfter(startDate.subtract(const Duration(days: 1))) &&
               transactionDate.isBefore(endDate.add(const Duration(days: 1)));
      }).toList();

      if (filteredTransactions.isEmpty) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('No transactions found in selected date range'),
              backgroundColor: Colors.orange,
            ),
          );
        }
        return;
      }

      // Use the file download service
      final success = await FileDownloadService.downloadStatement(
        context: context,
        transactions: filteredTransactions,
        startDate: startDate,
        endDate: endDate,
        format: format,
      );

      if (mounted && !success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to download $format statement'),
            backgroundColor: AppTheme.errorRed,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error downloading statement: ${e.toString()}'),
            backgroundColor: AppTheme.errorRed,
          ),
        );
      }
    }
  }
}

/// Download options bottom sheet
class _DownloadOptionsSheet extends StatefulWidget {
  final DateTime startDate;
  final DateTime endDate;
  final Function({
    required DateTime startDate,
    required DateTime endDate,
    required String format,
  }) onDownload;

  const _DownloadOptionsSheet({
    required this.startDate,
    required this.endDate,
    required this.onDownload,
  });

  @override
  State<_DownloadOptionsSheet> createState() => _DownloadOptionsSheetState();
}

class _DownloadOptionsSheetState extends State<_DownloadOptionsSheet> {
  String _selectedFormat = 'pdf';

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
      decoration: BoxDecoration(
        color: isDark ? AppTheme.backgroundDark : AppTheme.backgroundLight,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      padding: EdgeInsets.all(6.w),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              width: 12.w,
              height: 0.5.h,
              decoration: BoxDecoration(
                color: AppTheme.textMediumEmphasisLight,
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            SizedBox(height: 3.h),

            // Title
            Text(
              'Download Statement',
              style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),

            SizedBox(height: 1.h),

            // Date range
            Text(
              '${FormattingService.instance.formatDate(widget.startDate)} - ${FormattingService.instance.formatDate(widget.endDate)}',
              style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                color: AppTheme.textMediumEmphasisLight,
              ),
            ),

            SizedBox(height: 4.h),

            // Format selection
            Text(
              'Select Format',
              style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),

            SizedBox(height: 2.h),

            Row(
              children: [
                Expanded(
                  child: _buildFormatOption('PDF', 'pdf', 'picture_as_pdf'),
                ),
                SizedBox(width: 3.w),
                Expanded(
                  child: _buildFormatOption('Excel', 'xlsx', 'table_chart'),
                ),
              ],
            ),

            SizedBox(height: 4.h),

            // Download button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _downloadStatement,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryTeal,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(vertical: 2.h),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                icon: CustomIconWidget(
                  iconName: 'play_circle_filled',
                  color: Colors.white,
                  size: 20,
                ),
                label: Text(
                  'Watch Ad to Download',
                  style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),

            SizedBox(height: 2.h),
          ],
        ),
      ),
    );
  }

  Widget _buildFormatOption(String label, String value, String iconName) {
    final isSelected = _selectedFormat == value;

    return GestureDetector(
      onTap: () => setState(() => _selectedFormat = value),
      child: Container(
        padding: EdgeInsets.all(4.w),
        decoration: BoxDecoration(
          color: isSelected ? AppTheme.primaryTeal.withValues(alpha: 0.1) : Colors.transparent,
          border: Border.all(
            color: isSelected ? AppTheme.primaryTeal : AppTheme.borderSubtle,
            width: 2,
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            CustomIconWidget(
              iconName: iconName,
              color: isSelected ? AppTheme.primaryTeal : AppTheme.textMediumEmphasisLight,
              size: 32,
            ),
            SizedBox(height: 1.h),
            Text(
              label,
              style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                color: isSelected ? AppTheme.primaryTeal : AppTheme.textMediumEmphasisLight,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _downloadStatement() {
    Navigator.pop(context);
    widget.onDownload(
      startDate: widget.startDate,
      endDate: widget.endDate,
      format: _selectedFormat,
    );
  }
}
